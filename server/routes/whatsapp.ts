import { Router, Request, Response } from 'express';
import { whatsAppService } from '../services/WhatsAppService';
import { whatsAppMessageParser } from '../services/WhatsAppMessageParser';
import { whatsAppTemplateService } from '../services/WhatsAppTemplateService';
import { whatsAppFlowService } from '../services/WhatsAppFlowService';
import { ownerWhatsAppService } from '../services/OwnerWhatsAppService';
import { logger } from '../services/LoggerService';
import { config } from '../config';
import { asyncHandler } from '../utils/asyncHandler';
import { sendSuccess, sendError } from '@shared/api-response-utils';
import rateLimit from 'express-rate-limit';

const router = Router();

// Rate limiting for webhook endpoint
const webhookRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many webhook requests from this IP',
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Webhook verification endpoint for WhatsApp
 * This is called by Twilio to verify the webhook URL
 */
router.get('/webhook', asyncHandler(async (req: Request, res: Response) => {
  const mode = req.query['hub.mode'] as string;
  const token = req.query['hub.verify_token'] as string;
  const challenge = req.query['hub.challenge'] as string;

  logger.info('WhatsApp webhook verification attempt', 'whatsapp-webhook', { mode, token });

  // Verify the webhook token matches our configuration
  if (mode === 'subscribe' && token === config.whatsapp.webhookToken) {
    logger.info('WhatsApp webhook verified successfully', 'whatsapp-webhook');
    res.status(200).send(challenge);
  } else {
    logger.warn('WhatsApp webhook verification failed', 'whatsapp-webhook', { 
      expectedToken: config.whatsapp.webhookToken,
      receivedToken: token 
    });
    res.status(403).send('Forbidden');
    return;
  }
}));

/**
 * Webhook endpoint for receiving WhatsApp messages
 * This receives incoming messages from users
 */
router.post('/webhook', 
  webhookRateLimit,
  asyncHandler(async (req: Request, res: Response) => {
    const signature = req.get('X-Twilio-Signature') || '';
    const url = req.protocol + '://' + req.get('host') + req.originalUrl;
    
    logger.info('Received WhatsApp webhook', 'whatsapp-webhook', { 
      signature: signature ? 'present' : 'missing'
    });

    // Validate webhook signature in production
    if (config.app.nodeEnv === 'production') {
      const payload = JSON.stringify(req.body);
      const isValid = whatsAppService.validateWebhookSignature(payload, signature, url);
      
      if (!isValid) {
        logger.warn('Invalid WhatsApp webhook signature');
        return res.status(403).send('Forbidden');
      }
    }

    try {
      // Parse the incoming message
      const message = whatsAppService.parseIncomingMessage(req.body);
      
      if (!message) {
        logger.warn('Failed to parse WhatsApp message');
        return res.status(400).send('Invalid message format');
      }

      logger.info('Parsed WhatsApp message', 'whatsapp-webhook', {
        from: message.from,
        messageId: message.messageId
      });

      // Process the message asynchronously (don't block webhook response)
      setImmediate(() => {
        processIncomingMessage(message);
      });

      // Respond to Twilio immediately
      res.status(200).send('OK');

    } catch (error) {
      logger.error('Error processing WhatsApp webhook:', error as Error);
      res.status(500).send('Internal Server Error');
    }
  })
);

/**
 * Test endpoint for sending WhatsApp messages (development only)
 */
if (config.app.nodeEnv === 'development') {
  router.post('/send-test', asyncHandler(async (req: Request, res: Response) => {
    const { to, message } = req.body;

    if (!to || !message) {
      return res.status(400).json({ 
        error: 'Missing required fields: to, message' 
      });
    }

    const success = await whatsAppService.sendMessage(to, message);
    
    return res.json({ 
      success,
      message: success ? 'Message sent' : 'Failed to send message'
    });
  }));
}

/**
 * Status endpoint for WhatsApp service health check
 */
router.get('/status', asyncHandler(async (req: Request, res: Response) => {
  const status = whatsAppService.getStatus();
  res.json(status);
}));

/**
 * Get all available flows
 */
router.get('/flows', asyncHandler(async (_req: Request, res: Response) => {
  const flows = whatsAppFlowService.getAllFlows();
  
  return sendSuccess(res, {
    flows: flows.map(flow => ({
      id: flow.id,
      name: flow.name,
      version: flow.version,
      screenCount: flow.screens.length,
      screens: flow.screens.map(s => ({
        id: s.id,
        title: s.title
      }))
    }))
  });
}));

/**
 * Get specific flow details
 */
router.get('/flows/:flowId', asyncHandler(async (req: Request, res: Response) => {
  const { flowId } = req.params;
  const flow = whatsAppFlowService.getFlow(flowId);
  
  if (!flow) {
    return sendError(res, 'Flow not found', 404);
  }
  
  return sendSuccess(res, { flow });
}));

/**
 * Test flow endpoint (development only)
 */
if (config.app.nodeEnv === 'development') {
  router.post('/test-flow', asyncHandler(async (req: Request, res: Response) => {
    const { userId, flowId, action, data } = req.body;
    
    if (!userId || !flowId) {
      return sendError(res, 'Missing required fields: userId, flowId', 400);
    }
    
    if (action === 'start') {
      // Start a new flow
      const firstScreen = await whatsAppFlowService.startFlow(userId, flowId);
      
      if (!firstScreen) {
        return sendError(res, 'Failed to start flow', 500);
      }
      
      return sendSuccess(res, { 
        message: 'Flow started',
        screen: firstScreen 
      });
    } else if (action === 'respond') {
      // Process flow response
      const state = whatsAppFlowService.getUserFlowState(userId);
      
      if (!state) {
        return sendError(res, 'No active flow for user', 400);
      }
      
      const { nextScreen, result } = await whatsAppFlowService.processFlowResponse(
        userId,
        state.currentScreen,
        data || {}
      );
      
      return sendSuccess(res, {
        message: 'Response processed',
        nextScreen,
        result
      });
    } else if (action === 'cancel') {
      // Cancel flow
      const cancelled = whatsAppFlowService.cancelFlow(userId);
      
      return sendSuccess(res, {
        message: cancelled ? 'Flow cancelled' : 'No active flow to cancel'
      });
    } else {
      return sendError(res, 'Invalid action. Use: start, respond, or cancel', 400);
    }
  }));
}

/**
 * Process incoming WhatsApp message
 * This function handles the business logic for incoming messages
 */
async function processIncomingMessage(message: { from: string; body: string; messageId: string; timestamp: Date, to?: string }) {
  try {
    logger.info('Processing WhatsApp message', 'whatsapp-process', {
      from: message.from,
      to: message.to || 'unknown'
    });

    // OWNER-SPECIFIC ROUTING: Check if message is sent to an owner's number
    if (message.to && message.to !== config.whatsapp.twilioNumber) {
      // This message was sent to a specific owner's WhatsApp number
      logger.info('Routing to owner-specific flow', 'whatsapp-owner', {
        customer: message.from,
        owner: message.to
      });
      
      await ownerWhatsAppService.processCustomerMessage(
        message.from,
        message.to,
        message.body
      );
      return;
    }

    // PLATFORM FLOW: Continue with existing generic flows
    // Check if user has an active flow
    const activeFlow = whatsAppFlowService.getUserFlowState(message.from);
    
    if (activeFlow) {
      // Process flow response
      logger.info('Processing platform flow response', 'whatsapp-flow', {
        from: message.from,
        flowId: activeFlow.flowId,
        screen: activeFlow.currentScreen
      });

      // Handle flow cancellation
      if (message.body.toLowerCase() === 'cancel' || message.body.toLowerCase() === 'exit') {
        whatsAppFlowService.cancelFlow(message.from);
        await whatsAppService.sendMessage(message.from, '❌ Flow cancelled. Type "help" to see available options.');
        return;
      }

      // Process the flow response
      const { nextScreen, result } = await whatsAppFlowService.processFlowResponse(
        message.from,
        activeFlow.currentScreen,
        { response: message.body }
      );

      if (nextScreen) {
        // Send next screen to user
        const screenMessage = formatFlowScreen(nextScreen);
        await whatsAppService.sendMessage(message.from, screenMessage);
      } else if (result) {
        // Flow completed
        await whatsAppService.sendMessage(message.from, result.message || '✅ Flow completed successfully!');
      }

      logger.info('Platform flow response processed', 'whatsapp-flow', {
        from: message.from,
        hasNextScreen: !!nextScreen,
        hasResult: !!result
      });
      return;
    }

    // Parse the message to understand user intent
    const parsedCommand = whatsAppMessageParser.parseMessage(message.body);
    
    logger.info('Parsed WhatsApp command for platform', 'whatsapp-parse', {
      from: message.from,
      intent: parsedCommand.intent,
      confidence: parsedCommand.confidence
    });

    // Check if we should start a flow based on intent
    let flowToStart: string | null = null;
    
    if (parsedCommand.intent === 'booking' && parsedCommand.confidence > 0.6) {
      flowToStart = 'property_booking';
    } else if (parsedCommand.intent === 'availability' && parsedCommand.confidence > 0.6) {
      flowToStart = 'date_picker';
    } else if (message.body.toLowerCase().includes('flow') || message.body.toLowerCase() === 'book') {
      flowToStart = 'property_booking';
    }

    if (flowToStart) {
      // Start the platform flow
      const firstScreen = await whatsAppFlowService.startFlow(message.from, flowToStart);
      
      if (firstScreen) {
        const screenMessage = formatFlowScreen(firstScreen);
        await whatsAppService.sendMessage(message.from, screenMessage);
        
        logger.info('Started platform flow for user', 'whatsapp-flow', {
          from: message.from,
          flowId: flowToStart
        });
        return;
      }
    }

    // Check if we should use a template for better UX
    const templateId = whatsAppTemplateService.getTemplateForCommand(parsedCommand);
    
    if (templateId && parsedCommand.confidence > 0.7) {
      // Use template for high-confidence commands
      const success = await whatsAppTemplateService.sendTemplate(message.from, templateId);
      
      if (success) {
        logger.info('Sent template response to WhatsApp user', 'whatsapp-template', { 
          to: message.from,
          templateId
        });
        return;
      }
    }

    // Fallback to platform response with owner information
    let responseMessage = whatsAppMessageParser.getSuggestedResponse(parsedCommand);
    
    if (!responseMessage) {
      responseMessage = `🤖 Welcome to BookAFarm!\n\n` +
        `I can help you with:\n` +
        `🏡 Book a farmhouse\n` +
        `📅 Check availability\n` +
        `📞 Contact property owners\n` +
        `❓ Get information\n\n` +
        `What would you like to do today?\n\n` +
        `Reply with 'book' to start booking or 'help' for more options.`;
    }
    
    await whatsAppService.sendMessage(message.from, responseMessage);
    logger.info('Sent platform response to WhatsApp user', 'whatsapp-response', { 
      to: message.from,
      hadFlow: false
    });

  } catch (error) {
    logger.error('Error processing WhatsApp message:', error as Error);
    
    // Send error message to user
    await whatsAppService.sendMessage(
      message.from, 
      '❌ Sorry, something went wrong. Please try again or type "help" for assistance.'
    );
  }
}

/**
 * Format flow screen for WhatsApp message
 */
function formatFlowScreen(screen: any): string {
  let message = `${screen.title}\n`;
  message += '━━━━━━━━━━━━━━━━━━━━\n\n';

  // Format components based on type
  screen.layout.children.forEach((component: any) => {
    switch (component.type) {
      case 'TextHeading':
        message += `📌 ${component.props.text}\n\n`;
        break;
      
      case 'TextBody':
        message += `${component.props.text}\n\n`;
        break;
      
      case 'RadioButtonsGroup':
        if (component.props.data_source) {
          component.props.data_source.forEach((option: any, index: number) => {
            message += `${index + 1}. ${option.title}\n`;
            if (option.description) {
              message += `   ${option.description}\n`;
            }
          });
          message += '\n📝 Reply with the number of your choice\n\n';
        }
        break;
      
      case 'DatePicker':
        message += `📅 ${component.props.label}\n`;
        message += `   ${component.props.helper_text || 'Enter date (DD/MM/YYYY)'}\n\n`;
        break;
      
      case 'TextInput':
        message += `✏️ ${component.props.label}\n`;
        if (component.props.helper_text) {
          message += `   ${component.props.helper_text}\n`;
        }
        message += '\n';
        break;
      
      case 'Dropdown':
        message += `📋 ${component.props.label}:\n`;
        if (component.props.data_source) {
          component.props.data_source.forEach((option: any, index: number) => {
            message += `${index + 1}. ${option.title}\n`;
          });
          message += '\n';
        }
        break;
      
      case 'CheckboxGroup':
        message += `☑️ ${component.props.label}:\n`;
        if (component.props.data_source) {
          component.props.data_source.forEach((option: any) => {
            message += `• ${option.title}\n`;
          });
          message += '\n📝 Reply with numbers (e.g., "1,3,5") or "none"\n\n';
        }
        break;
      
      case 'Footer':
        message += `\n➡️ ${component.props.label}\n`;
        break;
    }
  });

  message += '\n💡 Type "cancel" to exit the flow';
  
  return message;
}


export default router;