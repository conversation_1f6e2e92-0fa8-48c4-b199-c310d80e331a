import { logger } from './LoggerService';

export interface ParsedCommand {
  intent: 'booking' | 'availability' | 'cancel' | 'help' | 'status' | 'unknown';
  entities: {
    dates?: {
      checkIn?: Date;
      checkOut?: Date;
      rawDates?: string;
    };
    guests?: number;
    location?: string;
    bookingRef?: string;
    phoneNumber?: string;
  };
  confidence: number;
  originalMessage: string;
}

export class WhatsAppMessageParser {
  
  /**
   * Parse incoming WhatsApp message to extract intent and entities
   */
  parseMessage(message: string): ParsedCommand {
    const cleanMessage = message.toLowerCase().trim();
    
    logger.debug('Parsing WhatsApp message', 'whatsapp-parser', { message: cleanMessage });

    const result: ParsedCommand = {
      intent: 'unknown',
      entities: {},
      confidence: 0,
      originalMessage: message
    };

    // Determine intent based on keywords
    result.intent = this.extractIntent(cleanMessage);
    result.confidence = this.calculateConfidence(cleanMessage, result.intent);

    // Extract entities based on intent
    switch (result.intent) {
      case 'booking':
        result.entities = {
          ...result.entities,
          ...this.extractBookingEntities(cleanMessage)
        };
        break;
      
      case 'availability':
        result.entities = {
          ...result.entities,
          ...this.extractAvailabilityEntities(cleanMessage)
        };
        break;
      
      case 'cancel':
        result.entities = {
          ...result.entities,
          ...this.extractCancellationEntities(cleanMessage)
        };
        break;
      
      case 'status':
        result.entities = {
          ...result.entities,
          ...this.extractStatusEntities(cleanMessage)
        };
        break;
    }

    logger.debug('Parsed message result', result);
    return result;
  }

  /**
   * Extract intent from message using keyword matching
   */
  private extractIntent(message: string): ParsedCommand['intent'] {
    const bookingKeywords = [
      'book', 'booking', 'reserve', 'reservation', 'rent', 'hire',
      'i want', 'i need', 'looking for', 'farmhouse'
    ];
    
    const availabilityKeywords = [
      'available', 'availability', 'check', 'free', 'vacant',
      'open dates', 'when is', 'is there'
    ];
    
    const cancelKeywords = [
      'cancel', 'cancellation', 'cancel booking', 'cancel reservation',
      'refund', 'delete booking'
    ];
    
    const statusKeywords = [
      'status', 'booking status', 'my booking', 'confirmation',
      'details', 'information about'
    ];
    
    const helpKeywords = [
      'help', 'hi', 'hello', 'hey', 'start', 'menu', 'options',
      'what can', 'how to', 'assist'
    ];

    // Check for booking intent (highest priority for business)
    if (this.containsKeywords(message, bookingKeywords)) {
      return 'booking';
    }
    
    // Check for availability
    if (this.containsKeywords(message, availabilityKeywords)) {
      return 'availability';
    }
    
    // Check for cancellation
    if (this.containsKeywords(message, cancelKeywords)) {
      return 'cancel';
    }
    
    // Check for status inquiry
    if (this.containsKeywords(message, statusKeywords)) {
      return 'status';
    }
    
    // Check for help/greeting
    if (this.containsKeywords(message, helpKeywords)) {
      return 'help';
    }

    return 'unknown';
  }

  /**
   * Calculate confidence score for the extracted intent
   */
  private calculateConfidence(message: string, intent: ParsedCommand['intent']): number {
    let confidence = 0.5; // Base confidence

    // Boost confidence for explicit keywords
    const explicitKeywords = {
      booking: ['book farmhouse', 'make booking', 'reserve farmhouse'],
      availability: ['check availability', 'available dates'],
      cancel: ['cancel booking', 'cancel reservation'],
      status: ['booking status', 'my booking'],
      help: ['help', 'hello', 'hi']
    };

    if (intent !== 'unknown' && explicitKeywords[intent]) {
      const hasExplicitKeyword = explicitKeywords[intent].some(keyword => 
        message.includes(keyword)
      );
      if (hasExplicitKeyword) {
        confidence = Math.min(0.9, confidence + 0.3);
      }
    }

    // Reduce confidence for very short messages
    if (message.length < 5) {
      confidence *= 0.7;
    }

    // Boost confidence for longer, detailed messages
    if (message.length > 20) {
      confidence = Math.min(0.95, confidence + 0.1);
    }

    return Math.round(confidence * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Extract booking-related entities
   */
  private extractBookingEntities(message: string): ParsedCommand['entities'] {
    const entities: ParsedCommand['entities'] = {};

    // Extract dates
    entities.dates = this.extractDates(message);
    
    // Extract guest count
    entities.guests = this.extractGuestCount(message);
    
    // Extract location
    entities.location = this.extractLocation(message);

    return entities;
  }

  /**
   * Extract availability-related entities
   */
  private extractAvailabilityEntities(message: string): ParsedCommand['entities'] {
    return {
      dates: this.extractDates(message),
      location: this.extractLocation(message),
      guests: this.extractGuestCount(message)
    };
  }

  /**
   * Extract cancellation-related entities
   */
  private extractCancellationEntities(message: string): ParsedCommand['entities'] {
    const entities: ParsedCommand['entities'] = {};

    // Extract booking reference
    entities.bookingRef = this.extractBookingReference(message);
    
    // Extract phone number
    entities.phoneNumber = this.extractPhoneNumber(message);

    return entities;
  }

  /**
   * Extract status inquiry entities
   */
  private extractStatusEntities(message: string): ParsedCommand['entities'] {
    return {
      bookingRef: this.extractBookingReference(message),
      phoneNumber: this.extractPhoneNumber(message)
    };
  }

  /**
   * Extract date information from message
   */
  private extractDates(message: string): ParsedCommand['entities']['dates'] {
    const datePatterns = [
      // MM/DD format
      /(\d{1,2})[\/\-](\d{1,2})/g,
      // Month day format
      /(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\w*\s+(\d{1,2})/gi,
      // Day month format
      /(\d{1,2})\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/gi,
      // Tomorrow, today, etc.
      /(tomorrow|today|this\s+weekend|next\s+week|next\s+weekend)/gi
    ];

    const dateMatches = [];
    for (const pattern of datePatterns) {
      const matches = message.match(pattern);
      if (matches) {
        dateMatches.push(...matches);
      }
    }

    if (dateMatches.length === 0) {
      return undefined;
    }

    return {
      rawDates: dateMatches.join(', '),
      // TODO: Implement proper date parsing logic
      // For now, just store the raw extracted dates
    };
  }

  /**
   * Extract guest count from message
   */
  private extractGuestCount(message: string): number | undefined {
    const guestPatterns = [
      /(\d+)\s+(people|persons|guests|adults)/gi,
      /(\d+)\s+(ppl|pax)/gi,
      /for\s+(\d+)/gi,
      /(\d+)\s+members/gi
    ];

    for (const pattern of guestPatterns) {
      const match = message.match(pattern);
      if (match) {
        const count = parseInt(match[1]);
        if (count > 0 && count <= 50) { // Reasonable limits
          return count;
        }
      }
    }

    return undefined;
  }

  /**
   * Extract location from message
   */
  private extractLocation(message: string): string | undefined {
    const locationKeywords = [
      'gurgaon', 'gurugram', 'delhi', 'noida', 'ghaziabad',
      'faridabad', 'manesar', 'sohna', 'bhiwadi', 'neemrana'
    ];

    for (const location of locationKeywords) {
      if (message.includes(location.toLowerCase())) {
        return location.charAt(0).toUpperCase() + location.slice(1);
      }
    }

    return undefined;
  }

  /**
   * Extract booking reference from message
   */
  private extractBookingReference(message: string): string | undefined {
    const refPatterns = [
      /(?:ref|reference|booking|id)[:\s#]*([a-zA-Z0-9]{6,})/gi,
      /([A-Z]{2,}\d{4,})/g, // Pattern like ABC1234
      /#([a-zA-Z0-9]{6,})/g // Hash reference
    ];

    for (const pattern of refPatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1] || match[0];
      }
    }

    return undefined;
  }

  /**
   * Extract phone number from message
   */
  private extractPhoneNumber(message: string): string | undefined {
    const phonePatterns = [
      /(\+91[-\s]?\d{10})/g,
      /(\d{10})/g,
      /(91\d{10})/g
    ];

    for (const pattern of phonePatterns) {
      const match = message.match(pattern);
      if (match) {
        const phone = match[0].replace(/[-\s]/g, '');
        if (phone.length >= 10) {
          return phone;
        }
      }
    }

    return undefined;
  }

  /**
   * Check if message contains any of the given keywords
   */
  private containsKeywords(message: string, keywords: string[]): boolean {
    return keywords.some(keyword => message.includes(keyword.toLowerCase()));
  }

  /**
   * Get suggested responses based on parsed command
   */
  getSuggestedResponse(command: ParsedCommand): string {
    switch (command.intent) {
      case 'booking':
        return this.getBookingResponse(command);
      case 'availability':
        return this.getAvailabilityResponse(command);
      case 'cancel':
        return this.getCancellationResponse(command);
      case 'status':
        return this.getStatusResponse(command);
      case 'help':
        return this.getHelpResponse();
      default:
        return this.getUnknownResponse();
    }
  }

  private getBookingResponse(command: ParsedCommand): string {
    const { dates, guests, location } = command.entities;
    
    let response = "Great! I'd love to help you book a farmhouse. 🏡\n\n";
    
    if (dates?.rawDates) {
      response += `📅 Dates: ${dates.rawDates}\n`;
    } else {
      response += "📅 What dates are you looking for?\n";
    }
    
    if (guests) {
      response += `👥 Guests: ${guests} people\n`;
    } else {
      response += "👥 How many guests?\n";
    }
    
    if (location) {
      response += `📍 Location: ${location}\n`;
    } else {
      response += "📍 Preferred location?\n";
    }
    
    response += "\nOnce I have all details, I can show you available properties and help you book!";
    
    return response;
  }

  private getAvailabilityResponse(command: ParsedCommand): string {
    return "Let me check availability for you! 📊\n\nTo show you the best options, please share:\n• Your preferred dates\n• Number of guests\n• Location preference\n\nI'll then show you all available farmhouses with pricing.";
  }

  private getCancellationResponse(command: ParsedCommand): string {
    const { bookingRef, phoneNumber } = command.entities;
    
    if (bookingRef) {
      return `I'll help you cancel booking ${bookingRef}. Let me check the details and cancellation policy for you.`;
    } else if (phoneNumber) {
      return `I'll look up bookings for ${phoneNumber}. Please wait while I check our records.`;
    } else {
      return "To cancel your booking, please provide:\n• Your booking reference number, OR\n• The phone number used for booking\n\nExample: 'Cancel booking REF123'";
    }
  }

  private getStatusResponse(command: ParsedCommand): string {
    return "I'll check your booking status. Please provide your booking reference number or the phone number used for booking.";
  }

  private getHelpResponse(): string {
    return `Welcome to Farmhouse Rentals! 🏡\n\nI can help you with:\n\n🏠 Book farmhouses\n📊 Check availability\n📋 View booking status\n❌ Cancel bookings\n\nJust tell me what you need in plain language!\n\nExample: "Book farmhouse for Dec 15-17, 8 people, Gurgaon"`;
  }

  private getUnknownResponse(): string {
    return "I didn't quite understand that. Could you please rephrase?\n\nI can help with:\n• Booking farmhouses\n• Checking availability\n• Booking status\n• Cancellations\n\nType 'help' for more options.";
  }
}

// Export singleton instance
export const whatsAppMessageParser = new WhatsAppMessageParser();