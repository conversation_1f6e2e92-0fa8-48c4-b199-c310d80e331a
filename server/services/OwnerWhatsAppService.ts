import { logger } from './LoggerService';
import { whatsAppService } from './WhatsAppService';
import { db } from '../db';
import { properties, users, calendarBookings } from '@shared/schema';
import { eq, and, desc } from 'drizzle-orm';
import { calendarService } from './CalendarService';

/**
 * Owner-specific WhatsApp Booking Service
 * Handles bookings directly with property owners
 */

export interface OwnerBookingFlow {
  customerId: string; // Customer's WhatsApp number
  ownerId: number;    // Property owner's ID
  ownerWhatsApp: string; // Owner's WhatsApp number
  step: 'property_inquiry' | 'date_selection' | 'guest_details' | 'confirmation' | 'completed';
  data: {
    propertyId?: number;
    propertyName?: string;
    checkIn?: string;
    checkOut?: string;
    stayType?: 'morning' | 'full_day';
    guestCount?: number;
    customerName?: string;
    customerPhone?: string;
    specialRequests?: string;
    totalPrice?: number;
  };
  startedAt: Date;
  lastUpdated: Date;
}

export class OwnerWhatsAppService {
  private activeBookings: Map<string, OwnerBookingFlow> = new Map();

  constructor() {
    logger.info('Owner WhatsApp Service initialized');
  }

  /**
   * Find owner by WhatsApp number
   */
  async findOwnerByWhatsApp(whatsappNumber: string): Promise<any | null> {
    try {
      // Clean the WhatsApp number format
      const cleanNumber = whatsappNumber.replace('whatsapp:', '').replace('+', '');
      
      // Try different number formats
      const numberVariants = [
        cleanNumber,
        `+${cleanNumber}`,
        `91${cleanNumber.slice(-10)}`, // Add India code if not present
        cleanNumber.slice(-10) // Just the 10-digit number
      ];

      for (const variant of numberVariants) {
        // First check WhatsApp number field
        const ownerByWhatsApp = await db.select({
          id: users.id,
          fullName: users.fullName,
          phone: users.phone,
          whatsappNumber: users.whatsappNumber,
          role: users.role
        })
        .from(users)
        .where(and(
          eq(users.whatsappNumber, variant),
          eq(users.role, 'owner')
        ))
        .limit(1);

        if (ownerByWhatsApp.length > 0) {
          return ownerByWhatsApp[0];
        }

        // Fallback to phone field if WhatsApp not set
        const ownerByPhone = await db.select({
          id: users.id,
          fullName: users.fullName,
          phone: users.phone,
          whatsappNumber: users.whatsappNumber,
          role: users.role
        })
        .from(users)
        .where(and(
          eq(users.phone, variant),
          eq(users.role, 'owner')
        ))
        .limit(1);

        if (ownerByPhone.length > 0) {
          return ownerByPhone[0];
        }
      }

      return null;
    } catch (error) {
      logger.error('Error finding owner by WhatsApp:', error as Error);
      return null;
    }
  }

  /**
   * Get owner's properties
   */
  async getOwnerProperties(ownerId: number): Promise<any[]> {
    try {
      const ownerProperties = await db.select({
        id: properties.id,
        title: properties.title,
        description: properties.description,
        location: properties.location,
        halfDayPrice: properties.halfDayPrice,
        fullDayPrice: properties.fullDayPrice,
        weekdayHalfDayPrice: properties.weekdayHalfDayPrice,
        weekdayFullDayPrice: properties.weekdayFullDayPrice,
        weekendHalfDayPrice: properties.weekendHalfDayPrice,
        weekendFullDayPrice: properties.weekendFullDayPrice,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        amenities: properties.amenities,
        status: properties.status
      })
      .from(properties)
      .where(and(
        eq(properties.ownerId, ownerId),
        eq(properties.status, 'active')
      ))
      .orderBy(desc(properties.featured), properties.title);

      return ownerProperties;
    } catch (error) {
      logger.error('Error fetching owner properties:', error as Error);
      return [];
    }
  }

  /**
   * Process customer message to owner
   */
  async processCustomerMessage(
    customerWhatsApp: string,
    ownerWhatsApp: string, 
    message: string
  ): Promise<void> {
    try {
      // Find the owner
      const owner = await this.findOwnerByWhatsApp(ownerWhatsApp);
      
      if (!owner) {
        await whatsAppService.sendMessage(
          customerWhatsApp,
          `❌ Sorry, I couldn't find the property owner. Please check the number and try again.`
        );
        return;
      }

      // Check if there's an active booking flow
      const flowKey = `${customerWhatsApp}_${owner.id}`;
      let bookingFlow = this.activeBookings.get(flowKey);

      if (!bookingFlow) {
        // Start new booking flow
        bookingFlow = await this.startOwnerBookingFlow(customerWhatsApp, owner, ownerWhatsApp);
      }

      // Process the message based on current step
      await this.processBookingStep(bookingFlow, message, customerWhatsApp);

    } catch (error) {
      logger.error('Error processing customer message:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Sorry, something went wrong. Please try again.`
      );
    }
  }

  /**
   * Start new booking flow with owner
   */
  private async startOwnerBookingFlow(
    customerWhatsApp: string,
    owner: any,
    ownerWhatsApp: string
  ): Promise<OwnerBookingFlow> {
    const bookingFlow: OwnerBookingFlow = {
      customerId: customerWhatsApp,
      ownerId: owner.id,
      ownerWhatsApp,
      step: 'property_inquiry',
      data: {},
      startedAt: new Date(),
      lastUpdated: new Date()
    };

    const flowKey = `${customerWhatsApp}_${owner.id}`;
    this.activeBookings.set(flowKey, bookingFlow);

    // Send welcome message and property list
    await this.sendPropertyList(customerWhatsApp, owner);

    logger.info('Started owner booking flow', 'owner-whatsapp', {
      customer: customerWhatsApp,
      owner: owner.fullName,
      ownerId: owner.id
    });

    return bookingFlow;
  }

  /**
   * Send property list to customer
   */
  private async sendPropertyList(customerWhatsApp: string, owner: any): Promise<void> {
    const properties = await this.getOwnerProperties(owner.id);

    if (properties.length === 0) {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `🏡 Welcome to ${owner.fullName}'s properties!\n\n` +
        `❌ Sorry, no properties are currently available for booking.\n\n` +
        `Please contact the owner directly for more information.`
      );
      return;
    }

    let message = `🏡 Welcome to ${owner.fullName}'s Farmhouse Rentals!\n`;
    message += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    message += `📋 Available Properties:\n\n`;

    properties.forEach((property, index) => {
      message += `${index + 1}. **${property.title}**\n`;
      message += `   📍 ${property.location}\n`;
      message += `   🛏️ ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms\n`;
      message += `   💰 Morning: ₹${property.halfDayPrice}/day\n`;
      message += `   💰 Full Day: ₹${property.fullDayPrice}/day\n`;
      
      if (property.amenities && Array.isArray(property.amenities)) {
        const amenitiesText = property.amenities.slice(0, 3).join(', ');
        message += `   ✨ ${amenitiesText}${property.amenities.length > 3 ? '...' : ''}\n`;
      }
      message += '\n';
    });

    message += `📝 **To book:**\n`;
    message += `Reply with the property number (1, 2, 3...) you'd like to book\n\n`;
    message += `💡 Or type "info 1" to get more details about a property`;

    await whatsAppService.sendMessage(customerWhatsApp, message);
  }

  /**
   * Process booking step
   */
  private async processBookingStep(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const flowKey = `${customerWhatsApp}_${bookingFlow.ownerId}`;
    
    switch (bookingFlow.step) {
      case 'property_inquiry':
        await this.handlePropertySelection(bookingFlow, message, customerWhatsApp);
        break;
      
      case 'date_selection':
        await this.handleDateSelection(bookingFlow, message, customerWhatsApp);
        break;
      
      case 'guest_details':
        await this.handleGuestDetails(bookingFlow, message, customerWhatsApp);
        break;
      
      case 'confirmation':
        await this.handleConfirmation(bookingFlow, message, customerWhatsApp);
        break;
      
      default:
        await whatsAppService.sendMessage(
          customerWhatsApp,
          `❓ I didn't understand that. Type "start" to begin booking again.`
        );
    }

    // Update flow
    bookingFlow.lastUpdated = new Date();
    this.activeBookings.set(flowKey, bookingFlow);
  }

  /**
   * Handle property selection
   */
  private async handlePropertySelection(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const ownerProperties = await this.getOwnerProperties(bookingFlow.ownerId);
    
    // Check if asking for more info
    if (message.toLowerCase().startsWith('info ')) {
      const propertyIndex = parseInt(message.split(' ')[1]) - 1;
      if (propertyIndex >= 0 && propertyIndex < ownerProperties.length) {
        await this.sendPropertyDetails(customerWhatsApp, ownerProperties[propertyIndex]);
        return;
      }
    }

    // Check property selection
    const selectedIndex = parseInt(message) - 1;
    
    if (selectedIndex >= 0 && selectedIndex < ownerProperties.length) {
      const selectedProperty = ownerProperties[selectedIndex];
      
      // Store property selection
      bookingFlow.data.propertyId = selectedProperty.id;
      bookingFlow.data.propertyName = selectedProperty.title;
      bookingFlow.step = 'date_selection';

      // Ask for dates
      let dateMessage = `✅ Great choice! **${selectedProperty.title}**\n\n`;
      dateMessage += `📅 **When would you like to visit?**\n`;
      dateMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      dateMessage += `Please provide your preferred dates:\n\n`;
      dateMessage += `📋 **Format examples:**\n`;
      dateMessage += `• "Dec 25" (single day)\n`;
      dateMessage += `• "Dec 25-26" (multiple days)\n`;
      dateMessage += `• "25/12/2024" (specific date)\n`;
      dateMessage += `• "This weekend"\n`;
      dateMessage += `• "Tomorrow"\n\n`;
      dateMessage += `⏰ **Stay type:**\n`;
      dateMessage += `• Morning visit (9 AM - 6 PM)\n`;
      dateMessage += `• Full day (check-in 2 PM next day)\n\n`;
      dateMessage += `💡 Example: "Dec 25, full day"`;

      await whatsAppService.sendMessage(customerWhatsApp, dateMessage);
    } else {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Please select a valid property number (1-${ownerProperties.length}) or type "info X" for details.`
      );
    }
  }

  /**
   * Send detailed property information
   */
  private async sendPropertyDetails(customerWhatsApp: string, property: any): Promise<void> {
    let detailMessage = `🏡 **${property.title}**\n`;
    detailMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    detailMessage += `📍 **Location:** ${property.location}\n`;
    detailMessage += `🛏️ **Accommodation:** ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms\n\n`;
    
    detailMessage += `💰 **Pricing:**\n`;
    detailMessage += `• Morning Visit: ₹${property.halfDayPrice}\n`;
    detailMessage += `• Full Day: ₹${property.fullDayPrice}\n`;
    
    if (property.weekdayHalfDayPrice) {
      detailMessage += `• Weekday Morning: ₹${property.weekdayHalfDayPrice}\n`;
      detailMessage += `• Weekday Full Day: ₹${property.weekdayFullDayPrice}\n`;
    }
    
    detailMessage += `\n📝 **Description:**\n${property.description}\n\n`;
    
    if (property.amenities && Array.isArray(property.amenities)) {
      detailMessage += `✨ **Amenities:**\n`;
      property.amenities.forEach((amenity: string) => {
        detailMessage += `• ${amenity}\n`;
      });
      detailMessage += '\n';
    }
    
    detailMessage += `📞 Reply with the property number to book this farmhouse!`;

    await whatsAppService.sendMessage(customerWhatsApp, detailMessage);
  }

  /**
   * Handle date selection
   */
  private async handleDateSelection(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    // Parse date and stay type from message
    const { dates, stayType } = this.parseDateMessage(message);
    
    if (!dates.checkIn) {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ I couldn't understand the date. Please use formats like:\n` +
        `• "Dec 25" or "25/12/2024"\n` +
        `• "Dec 25-26" for multiple days\n` +
        `• "Tomorrow" or "This weekend"\n\n` +
        `Also mention: "morning" or "full day"`
      );
      return;
    }

    // Store date information
    if (dates.checkIn) bookingFlow.data.checkIn = dates.checkIn;
    if (dates.checkOut) bookingFlow.data.checkOut = dates.checkOut;
    bookingFlow.data.stayType = stayType;
    bookingFlow.step = 'guest_details';

    // Ask for guest details
    let guestMessage = `📅 **Dates confirmed:**\n`;
    guestMessage += `Check-in: ${dates.checkIn}\n`;
    if (dates.checkOut && dates.checkOut !== dates.checkIn) {
      guestMessage += `Check-out: ${dates.checkOut}\n`;
    }
    guestMessage += `Stay type: ${stayType === 'morning' ? 'Morning Visit' : 'Full Day'}\n\n`;
    
    guestMessage += `👥 **Guest Information needed:**\n`;
    guestMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    guestMessage += `Please provide the following details:\n\n`;
    guestMessage += `📝 **Format:**\n`;
    guestMessage += `Name: [Your full name]\n`;
    guestMessage += `Phone: [Your contact number]\n`;
    guestMessage += `Guests: [Number of guests]\n\n`;
    guestMessage += `💡 **Example:**\n`;
    guestMessage += `Name: Rajesh Kumar\n`;
    guestMessage += `Phone: 9876543210\n`;
    guestMessage += `Guests: 8\n\n`;
    guestMessage += `📞 You can also add any special requests!`;

    await whatsAppService.sendMessage(customerWhatsApp, guestMessage);
  }

  /**
   * Parse date message to extract dates and stay type
   */
  private parseDateMessage(message: string): { dates: { checkIn: string; checkOut?: string }, stayType: 'morning' | 'full_day' } {
    const lowerMessage = message.toLowerCase();
    
    // Determine stay type
    let stayType: 'morning' | 'full_day' = 'full_day'; // default
    if (lowerMessage.includes('morning') || lowerMessage.includes('half day') || lowerMessage.includes('day visit')) {
      stayType = 'morning';
    }

    // Simple date parsing - this could be enhanced
    let checkIn = '';
    let checkOut = '';

    if (lowerMessage.includes('tomorrow')) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      checkIn = tomorrow.toISOString().split('T')[0];
      checkOut = checkIn;
    } else if (lowerMessage.includes('today')) {
      const today = new Date();
      checkIn = today.toISOString().split('T')[0];
      checkOut = checkIn;
    } else if (lowerMessage.includes('weekend')) {
      const today = new Date();
      const saturday = new Date(today);
      saturday.setDate(today.getDate() + (6 - today.getDay()));
      const sunday = new Date(saturday);
      sunday.setDate(saturday.getDate() + 1);
      
      checkIn = saturday.toISOString().split('T')[0];
      checkOut = sunday.toISOString().split('T')[0];
    } else {
      // Try to extract date from message
      const dateMatches = message.match(/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/);
      if (dateMatches) {
        const [, day, month, year] = dateMatches;
        checkIn = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        checkOut = checkIn;
      } else {
        // Try other date formats - simplified for demo
        const dateWords = message.match(/dec\s+(\d{1,2})(\-(\d{1,2}))?/i);
        if (dateWords) {
          const day1 = dateWords[1];
          const day2 = dateWords[3];
          const currentYear = new Date().getFullYear();
          
          checkIn = `${currentYear}-12-${day1.padStart(2, '0')}`;
          checkOut = day2 ? `${currentYear}-12-${day2.padStart(2, '0')}` : checkIn;
        }
      }
    }

    return {
      dates: { checkIn, checkOut },
      stayType
    };
  }

  /**
   * Handle guest details
   */
  private async handleGuestDetails(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    // Parse guest information
    const guestInfo = this.parseGuestInfo(message);
    
    if (!guestInfo.name || !guestInfo.phone || !guestInfo.guestCount) {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Please provide all required information:\n\n` +
        `Name: [Your full name]\n` +
        `Phone: [Your contact number]\n` +
        `Guests: [Number of guests]\n\n` +
        `You can also add special requests on separate lines.`
      );
      return;
    }

    // Store guest information
    bookingFlow.data.customerName = guestInfo.name;
    bookingFlow.data.customerPhone = guestInfo.phone;
    bookingFlow.data.guestCount = guestInfo.guestCount;
    bookingFlow.data.specialRequests = guestInfo.specialRequests;

    // Calculate pricing and move to confirmation
    await this.calculatePriceAndConfirm(bookingFlow, customerWhatsApp);
  }

  /**
   * Parse guest information from message
   */
  private parseGuestInfo(message: string): {
    name: string;
    phone: string;
    guestCount: number;
    specialRequests: string;
  } {
    let name = '';
    let phone = '';
    let guestCount = 0;
    let specialRequests = '';

    const lines = message.split('\n');
    let otherLines: string[] = [];

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.toLowerCase().startsWith('name:')) {
        name = trimmedLine.substring(5).trim();
      } else if (trimmedLine.toLowerCase().startsWith('phone:')) {
        phone = trimmedLine.substring(6).trim().replace(/\D/g, ''); // Remove non-digits
      } else if (trimmedLine.toLowerCase().startsWith('guests:')) {
        guestCount = parseInt(trimmedLine.substring(7).trim()) || 0;
      } else if (trimmedLine.length > 0) {
        otherLines.push(trimmedLine);
      }
    }

    // If no structured format, try to extract from free text
    if (!name) {
      const nameMatch = message.match(/([A-Za-z\s]{2,30})/);
      if (nameMatch) name = nameMatch[1].trim();
    }

    if (!phone) {
      const phoneMatch = message.match(/(\d{10,12})/);
      if (phoneMatch) phone = phoneMatch[1];
    }

    if (!guestCount) {
      const guestMatch = message.match(/(\d+)\s*(guest|people|person)/i);
      if (guestMatch) guestCount = parseInt(guestMatch[1]);
    }

    specialRequests = otherLines.join(' ').trim();

    return { name, phone, guestCount, specialRequests };
  }

  /**
   * Calculate price and send confirmation
   */
  private async calculatePriceAndConfirm(
    bookingFlow: OwnerBookingFlow,
    customerWhatsApp: string
  ): Promise<void> {
    try {
      // Get property details for pricing
      const property = await db.select()
        .from(properties)
        .where(eq(properties.id, bookingFlow.data.propertyId!))
        .limit(1);

      if (property.length === 0) {
        throw new Error('Property not found');
      }

      const prop = property[0];
      const isWeekend = this.isWeekendDate(bookingFlow.data.checkIn!);
      
      // Calculate base price
      let basePrice = 0;
      if (bookingFlow.data.stayType === 'morning') {
        basePrice = isWeekend && prop.weekendHalfDayPrice 
          ? prop.weekendHalfDayPrice 
          : prop.weekdayHalfDayPrice || prop.halfDayPrice;
      } else {
        basePrice = isWeekend && prop.weekendFullDayPrice 
          ? prop.weekendFullDayPrice 
          : prop.weekdayFullDayPrice || prop.fullDayPrice;
      }

      // Calculate total days
      const checkInDate = new Date(bookingFlow.data.checkIn!);
      const checkOutDate = new Date(bookingFlow.data.checkOut || bookingFlow.data.checkIn!);
      const days = Math.max(1, Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)) + 1);
      
      const totalPrice = basePrice * days;
      bookingFlow.data.totalPrice = totalPrice;
      bookingFlow.step = 'confirmation';

      // Send confirmation message
      let confirmMessage = `✅ **Booking Summary**\n`;
      confirmMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      confirmMessage += `🏡 **Property:** ${bookingFlow.data.propertyName}\n`;
      confirmMessage += `📅 **Date:** ${bookingFlow.data.checkIn}`;
      if (bookingFlow.data.checkOut && bookingFlow.data.checkOut !== bookingFlow.data.checkIn) {
        confirmMessage += ` to ${bookingFlow.data.checkOut}`;
      }
      confirmMessage += `\n⏰ **Stay:** ${bookingFlow.data.stayType === 'morning' ? 'Morning Visit' : 'Full Day'}\n`;
      confirmMessage += `👤 **Guest:** ${bookingFlow.data.customerName}\n`;
      confirmMessage += `📞 **Phone:** ${bookingFlow.data.customerPhone}\n`;
      confirmMessage += `👥 **Guests:** ${bookingFlow.data.guestCount}\n`;
      
      if (bookingFlow.data.specialRequests) {
        confirmMessage += `📝 **Special Requests:** ${bookingFlow.data.specialRequests}\n`;
      }
      
      confirmMessage += `\n💰 **Total Amount:** ₹${totalPrice}\n`;
      confirmMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      confirmMessage += `✅ **To confirm booking, reply:** "CONFIRM"\n`;
      confirmMessage += `❌ **To cancel, reply:** "CANCEL"\n`;
      confirmMessage += `✏️ **To modify, reply:** "CHANGE"`;

      await whatsAppService.sendMessage(customerWhatsApp, confirmMessage);

    } catch (error) {
      logger.error('Error calculating price:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Error calculating booking price. Please try again.`
      );
    }
  }

  /**
   * Handle confirmation
   */
  private async handleConfirmation(
    bookingFlow: OwnerBookingFlow,
    message: string,
    customerWhatsApp: string
  ): Promise<void> {
    const lowerMessage = message.toLowerCase().trim();

    if (lowerMessage === 'confirm' || lowerMessage === 'yes') {
      // Create the booking
      await this.createBooking(bookingFlow, customerWhatsApp);
    } else if (lowerMessage === 'cancel' || lowerMessage === 'no') {
      // Cancel the booking flow
      const flowKey = `${customerWhatsApp}_${bookingFlow.ownerId}`;
      this.activeBookings.delete(flowKey);
      
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Booking cancelled. Thank you for your interest! You can start a new booking anytime by messaging again.`
      );
    } else if (lowerMessage === 'change' || lowerMessage === 'modify') {
      // Reset to property selection
      bookingFlow.step = 'property_inquiry';
      bookingFlow.data = {};
      
      const owner = await db.select().from(users).where(eq(users.id, bookingFlow.ownerId)).limit(1);
      if (owner.length > 0) {
        await this.sendPropertyList(customerWhatsApp, owner[0]);
      }
    } else {
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `Please reply with:\n• "CONFIRM" to confirm booking\n• "CANCEL" to cancel\n• "CHANGE" to modify details`
      );
    }
  }

  /**
   * Create the actual booking and add to calendar
   */
  private async createBooking(bookingFlow: OwnerBookingFlow, customerWhatsApp: string): Promise<void> {
    try {
      const bookingRef = `WA${Date.now()}`;
      
      // Create or find customer user
      let customerId: number | undefined;
      const existingCustomer = await db.select()
        .from(users)
        .where(eq(users.phone, bookingFlow.data.customerPhone!))
        .limit(1);
      
      if (existingCustomer.length > 0) {
        customerId = existingCustomer[0].id;
      } else {
        // Create new customer user
        const [newCustomer] = await db.insert(users)
          .values({
            username: `customer_${bookingFlow.data.customerPhone}`,
            password: 'temp_password', // Will need to be changed
            email: `${bookingFlow.data.customerPhone}@whatsapp.com`,
            fullName: bookingFlow.data.customerName!,
            phone: bookingFlow.data.customerPhone!,
            role: 'user' as const
          })
          .returning();
        customerId = newCustomer.id;
      }

      // Add booking to calendar
      const calendarBooking = await calendarService.createCalendarBooking({
        propertyId: bookingFlow.data.propertyId!,
        startDate: bookingFlow.data.checkIn!,
        endDate: bookingFlow.data.checkOut || bookingFlow.data.checkIn!,
        guestName: bookingFlow.data.customerName!,
        guestPhone: bookingFlow.data.customerPhone!,
        guestCount: bookingFlow.data.guestCount!,
        bookingType: bookingFlow.data.stayType === 'morning' ? 'Morning Visit' : 'Full Day',
        totalAmount: bookingFlow.data.totalPrice!,
        status: 'confirmed',
        source: 'whatsapp',
        externalBookingId: bookingRef,
        notes: bookingFlow.data.specialRequests,
        createdBy: customerId
      });

      // Send confirmation to customer
      let confirmMessage = `🎉 **Booking Confirmed!**\n`;
      confirmMessage += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
      confirmMessage += `📝 **Booking Reference:** ${bookingRef}\n`;
      confirmMessage += `📅 **Calendar ID:** #${calendarBooking.id}\n`;
      confirmMessage += `🏡 **Property:** ${bookingFlow.data.propertyName}\n`;
      confirmMessage += `📅 **Date:** ${bookingFlow.data.checkIn}\n`;
      confirmMessage += `👥 **Guests:** ${bookingFlow.data.guestCount}\n`;
      confirmMessage += `💰 **Total:** ₹${bookingFlow.data.totalPrice}\n\n`;
      confirmMessage += `📞 **Owner will contact you shortly** for:\n`;
      confirmMessage += `• Payment details\n`;
      confirmMessage += `• Property directions\n`;
      confirmMessage += `• Final arrangements\n\n`;
      confirmMessage += `✅ Your booking has been added to the owner's calendar!`;

      await whatsAppService.sendMessage(customerWhatsApp, confirmMessage);

      // Send notification to owner with calendar update info
      const owner = await db.select().from(users).where(eq(users.id, bookingFlow.ownerId)).limit(1);
      if (owner.length > 0) {
        const ownerWhatsApp = owner[0].whatsappNumber || owner[0].phone;
        if (ownerWhatsApp) {
          const ownerNotification = `🔔 **New Booking Alert!**\n\n` +
            `📝 **Reference:** ${bookingRef}\n` +
            `📅 **Calendar Entry:** #${calendarBooking.id}\n` +
            `🏡 **Property:** ${bookingFlow.data.propertyName}\n` +
            `👤 **Customer:** ${bookingFlow.data.customerName}\n` +
            `📞 **Phone:** ${bookingFlow.data.customerPhone}\n` +
            `📅 **Date:** ${bookingFlow.data.checkIn}\n` +
            `👥 **Guests:** ${bookingFlow.data.guestCount}\n` +
            `💰 **Amount:** ₹${bookingFlow.data.totalPrice}\n` +
            `📱 **Customer WhatsApp:** ${customerWhatsApp}\n\n` +
            `✅ **This booking has been automatically added to your calendar.**\n` +
            `Please contact the customer to confirm payment and arrangements.`;

          // Send to owner's WhatsApp
          await whatsAppService.sendMessage(`whatsapp:${ownerWhatsApp}`, ownerNotification);
        }
      }

      // Mark flow as completed
      bookingFlow.step = 'completed';
      const flowKey = `${customerWhatsApp}_${bookingFlow.ownerId}`;
      this.activeBookings.delete(flowKey); // Clean up completed flow

      logger.info('Owner booking created successfully', 'owner-whatsapp', {
        bookingRef,
        customer: customerWhatsApp,
        owner: bookingFlow.ownerId,
        property: bookingFlow.data.propertyName
      });

    } catch (error) {
      logger.error('Error creating booking:', error as Error);
      await whatsAppService.sendMessage(
        customerWhatsApp,
        `❌ Sorry, there was an error creating your booking. Please try again or contact the owner directly.`
      );
    }
  }

  /**
   * Check if date is weekend
   */
  private isWeekendDate(dateString: string): boolean {
    const date = new Date(dateString);
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }

  /**
   * Get active booking for customer and owner
   */
  getActiveBooking(customerWhatsApp: string, ownerId: number): OwnerBookingFlow | undefined {
    const flowKey = `${customerWhatsApp}_${ownerId}`;
    return this.activeBookings.get(flowKey);
  }

  /**
   * Cancel active booking flow
   */
  cancelBookingFlow(customerWhatsApp: string, ownerId: number): boolean {
    const flowKey = `${customerWhatsApp}_${ownerId}`;
    return this.activeBookings.delete(flowKey);
  }

  /**
   * Get all active bookings (for admin/monitoring)
   */
  getAllActiveBookings(): OwnerBookingFlow[] {
    return Array.from(this.activeBookings.values());
  }
}

// Export singleton instance
export const ownerWhatsAppService = new OwnerWhatsAppService();