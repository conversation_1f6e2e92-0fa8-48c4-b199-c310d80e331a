import { logger } from './LoggerService';
import { db } from '../db';
import { calendarBookings } from '@shared/schema';
import { eq, and, gte, lte, or } from 'drizzle-orm';

/**
 * WhatsApp Calendar Service
 * Provides interactive calendar functionality for date selection in WhatsApp
 */
export class WhatsAppCalendarService {
  private monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  private dayNames = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];

  /**
   * Generate interactive calendar view for WhatsApp
   */
  async generateCalendarView(propertyId: number, month: number, year: number): Promise<string> {
    try {
      const availability = await this.getMonthAvailability(propertyId, month, year);
      const calendar = this.buildCalendarGrid(month, year, availability);
      
      return `📅 **${this.monthNames[month - 1]} ${year}**\n` +
             `🏡 Property Calendar\n\n` +
             `${calendar}\n\n` +
             `✅ Available  ❌ Booked  🟡 Pending\n\n` +
             `📝 **How to book:**\n` +
             `• Reply with day number (e.g., "15")\n` +
             `• For range: "15-18" or "15 to 18"\n` +
             `• Navigate: "next" or "prev" month\n` +
             `• Cancel: "back" or "cancel"`;
    } catch (error) {
      logger.error('Failed to generate calendar view', error as Error, 'whatsapp-calendar');
      return '❌ Unable to load calendar. Please try again.';
    }
  }

  /**
   * Get availability data for entire month
   */
  private async getMonthAvailability(propertyId: number, month: number, year: number): Promise<Map<number, 'available' | 'booked' | 'pending'>> {
    const availability = new Map<number, 'available' | 'booked' | 'pending'>();
    const daysInMonth = new Date(year, month, 0).getDate();
    
    // Initialize all days as available
    for (let day = 1; day <= daysInMonth; day++) {
      availability.set(day, 'available');
    }

    try {
      const monthStart = new Date(year, month - 1, 1);
      const monthEnd = new Date(year, month, 0);
      
      // Query existing bookings for the month
      const existingBookings = await db
        .select()
        .from(calendarBookings)
        .where(
          and(
            eq(calendarBookings.propertyId, propertyId),
            or(
              and(
                gte(calendarBookings.startDate, monthStart.toISOString().split('T')[0]),
                lte(calendarBookings.startDate, monthEnd.toISOString().split('T')[0])
              ),
              and(
                gte(calendarBookings.endDate, monthStart.toISOString().split('T')[0]),
                lte(calendarBookings.endDate, monthEnd.toISOString().split('T')[0])
              )
            )
          )
        );

      // Mark booked days
      existingBookings.forEach(booking => {
        const startDate = new Date(booking.startDate);
        const endDate = new Date(booking.endDate);
        
        // Mark all days in booking range
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
          if (d.getMonth() === month - 1) { // Check if day is in current month
            const dayNum = d.getDate();
            const status = booking.status === 'confirmed' ? 'booked' : 'pending';
            availability.set(dayNum, status);
          }
        }
      });

      // Mark past days as unavailable
      const today = new Date();
      if (year === today.getFullYear() && month === today.getMonth() + 1) {
        for (let day = 1; day < today.getDate(); day++) {
          availability.set(day, 'booked');
        }
      } else if (year < today.getFullYear() || (year === today.getFullYear() && month < today.getMonth() + 1)) {
        // Entire month is in the past
        for (let day = 1; day <= daysInMonth; day++) {
          availability.set(day, 'booked');
        }
      }

    } catch (error) {
      logger.error('Failed to fetch availability data', error as Error, 'whatsapp-calendar');
    }

    return availability;
  }

  /**
   * Build visual calendar grid
   */
  private buildCalendarGrid(month: number, year: number, availability: Map<number, 'available' | 'booked' | 'pending'>): string {
    const firstDay = new Date(year, month - 1, 1);
    const daysInMonth = new Date(year, month, 0).getDate();
    const startingDayOfWeek = (firstDay.getDay() + 6) % 7; // Monday = 0

    let calendar = '```\n'; // Use monospace formatting
    
    // Add day headers
    calendar += this.dayNames.join(' ') + '\n';
    calendar += '━━━━━━━━━━━━━━━━━━━━\n';

    let currentLine = '';
    
    // Add empty spaces for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
      currentLine += '   ';
    }

    // Add each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const status = availability.get(day) || 'available';
      const dayStr = day.toString().padStart(2, ' ');
      
      // Add status indicator
      let dayDisplay = '';
      switch (status) {
        case 'available':
          dayDisplay = `${dayStr} `;
          break;
        case 'booked':
          dayDisplay = `${dayStr}❌`;
          break;
        case 'pending':
          dayDisplay = `${dayStr}🟡`;
          break;
      }
      
      currentLine += dayDisplay;

      // Check if we need a new line (end of week)
      if ((startingDayOfWeek + day) % 7 === 0) {
        calendar += currentLine + '\n';
        currentLine = '';
      }
    }

    // Add remaining line if not complete
    if (currentLine.trim()) {
      calendar += currentLine + '\n';
    }

    calendar += '```\n';
    return calendar;
  }

  /**
   * Parse date selection from user input
   */
  parseDate Selection(input: string, currentMonth: number, currentYear: number): {
    valid: boolean;
    startDate?: Date;
    endDate?: Date;
    error?: string;
  } {
    const cleanInput = input.trim().toLowerCase();

    try {
      // Handle single day selection (e.g., "15")
      if (/^\d{1,2}$/.test(cleanInput)) {
        const day = parseInt(cleanInput);
        if (day < 1 || day > 31) {
          return { valid: false, error: 'Invalid day. Please enter a day between 1-31.' };
        }

        const selectedDate = new Date(currentYear, currentMonth - 1, day);
        if (selectedDate.getMonth() !== currentMonth - 1) {
          return { valid: false, error: `${this.monthNames[currentMonth - 1]} doesn't have ${day} days.` };
        }

        return {
          valid: true,
          startDate: selectedDate,
          endDate: selectedDate
        };
      }

      // Handle date range (e.g., "15-18", "15 to 18")
      const rangeMatch = cleanInput.match(/^(\d{1,2})[-\s]?(?:to|-)\s?(\d{1,2})$/);
      if (rangeMatch) {
        const startDay = parseInt(rangeMatch[1]);
        const endDay = parseInt(rangeMatch[2]);

        if (startDay < 1 || startDay > 31 || endDay < 1 || endDay > 31) {
          return { valid: false, error: 'Invalid days. Please enter days between 1-31.' };
        }

        if (startDay >= endDay) {
          return { valid: false, error: 'End date must be after start date.' };
        }

        const startDate = new Date(currentYear, currentMonth - 1, startDay);
        const endDate = new Date(currentYear, currentMonth - 1, endDay);

        if (startDate.getMonth() !== currentMonth - 1 || endDate.getMonth() !== currentMonth - 1) {
          return { valid: false, error: `Invalid date range for ${this.monthNames[currentMonth - 1]}.` };
        }

        return {
          valid: true,
          startDate,
          endDate
        };
      }

      // Handle full date format (DD/MM/YYYY)
      const fullDateMatch = cleanInput.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
      if (fullDateMatch) {
        const day = parseInt(fullDateMatch[1]);
        const month = parseInt(fullDateMatch[2]);
        const year = parseInt(fullDateMatch[3]);

        const selectedDate = new Date(year, month - 1, day);
        if (selectedDate.getMonth() !== month - 1) {
          return { valid: false, error: 'Invalid date format. Please use DD/MM/YYYY.' };
        }

        return {
          valid: true,
          startDate: selectedDate,
          endDate: selectedDate
        };
      }

      return { valid: false, error: 'Invalid date format. Try:\n• Single day: "15"\n• Date range: "15-18"\n• Full date: "15/03/2024"' };

    } catch (error) {
      logger.error('Failed to parse date selection', error as Error, 'whatsapp-calendar');
      return { valid: false, error: 'Unable to parse date. Please try again.' };
    }
  }

  /**
   * Generate quick date options for common selections
   */
  generateQuickDateOptions(): string {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const weekend = new Date(today);
    const daysUntilWeekend = 6 - today.getDay(); // Days until Saturday
    weekend.setDate(weekend.getDate() + daysUntilWeekend);

    return `🚀 **Quick Options:**\n\n` +
           `1. Today (${this.formatDateShort(today)})\n` +
           `2. Tomorrow (${this.formatDateShort(tomorrow)})\n` +
           `3. This Weekend (${this.formatDateShort(weekend)})\n` +
           `4. Next Week\n` +
           `5. Choose specific dates\n\n` +
           `Reply with number or "calendar" for full calendar view.`;
  }

  /**
   * Handle quick date option selection
   */
  parseQuickOption(option: string): {
    valid: boolean;
    startDate?: Date;
    endDate?: Date;
    showCalendar?: boolean;
  } {
    const today = new Date();
    
    switch (option.toLowerCase()) {
      case '1':
      case 'today':
        return {
          valid: true,
          startDate: today,
          endDate: today
        };
        
      case '2':
      case 'tomorrow': {
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        return {
          valid: true,
          startDate: tomorrow,
          endDate: tomorrow
        };
      }
      
      case '3':
      case 'weekend': {
        const weekend = new Date(today);
        const daysUntilSaturday = 6 - today.getDay();
        weekend.setDate(weekend.getDate() + daysUntilSaturday);
        
        const sunday = new Date(weekend);
        sunday.setDate(sunday.getDate() + 1);
        
        return {
          valid: true,
          startDate: weekend,
          endDate: sunday
        };
      }
      
      case '4':
      case 'next week': {
        const nextMonday = new Date(today);
        const daysUntilNextMonday = 8 - today.getDay();
        nextMonday.setDate(nextMonday.getDate() + daysUntilNextMonday);
        
        const nextFriday = new Date(nextMonday);
        nextFriday.setDate(nextFriday.getDate() + 4);
        
        return {
          valid: true,
          startDate: nextMonday,
          endDate: nextFriday
        };
      }
      
      case '5':
      case 'calendar':
      case 'choose':
        return {
          valid: true,
          showCalendar: true
        };
        
      default:
        return { valid: false };
    }
  }

  /**
   * Format date for display
   */
  private formatDateShort(date: Date): string {
    return date.toLocaleDateString('en-GB', { 
      day: '2-digit', 
      month: 'short'
    });
  }

  /**
   * Format date range for confirmation
   */
  formatDateRange(startDate: Date, endDate: Date): string {
    if (startDate.getTime() === endDate.getTime()) {
      return startDate.toLocaleDateString('en-GB', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    }

    return `${startDate.toLocaleDateString('en-GB', {
      weekday: 'short',
      day: 'numeric',
      month: 'short'
    })} to ${endDate.toLocaleDateString('en-GB', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })}`;
  }

  /**
   * Check if selected dates are available
   */
  async checkAvailability(propertyId: number, startDate: Date, endDate: Date): Promise<{
    available: boolean;
    conflictDates?: Date[];
    message: string;
  }> {
    try {
      // Query for conflicting bookings
      const conflicts = await db
        .select()
        .from(calendarBookings)
        .where(
          and(
            eq(calendarBookings.propertyId, propertyId),
            or(
              and(
                gte(calendarBookings.startDate, startDate.toISOString().split('T')[0]),
                lte(calendarBookings.startDate, endDate.toISOString().split('T')[0])
              ),
              and(
                gte(calendarBookings.endDate, startDate.toISOString().split('T')[0]),
                lte(calendarBookings.endDate, endDate.toISOString().split('T')[0])
              )
            )
          )
        );

      if (conflicts.length === 0) {
        return {
          available: true,
          message: `✅ **Available!**\n${this.formatDateRange(startDate, endDate)} is available for booking.`
        };
      }

      const conflictDates = conflicts.map(c => new Date(c.startDate));
      return {
        available: false,
        conflictDates,
        message: `❌ **Not Available**\n${this.formatDateRange(startDate, endDate)} has conflicts.\n\nPlease choose different dates.`
      };

    } catch (error) {
      logger.error('Failed to check availability', error as Error, 'whatsapp-calendar');
      return {
        available: false,
        message: '⚠️ Unable to check availability. Please try again.'
      };
    }
  }
}

// Export singleton instance
export const whatsAppCalendarService = new WhatsAppCalendarService();